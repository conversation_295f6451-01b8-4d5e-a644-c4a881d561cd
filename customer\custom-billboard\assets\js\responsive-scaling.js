// ========================================
// RESPONSIVE SCALING FOR DRAGGABLE ELEMENTS
// ========================================

// Extend CF7TextEditor class with responsive scaling methods
Object.assign(CF7TextEditor.prototype, {
    // Initialize responsive scaling
    initResponsiveScaling() {
        this.baseCanvasWidth = parseInt(this.canvas.dataset.width) || 800;
        this.baseCanvasHeight = parseInt(this.canvas.dataset.height) || 400;
        this.currentScale = 1;
        this.isResponsiveMode = false;

        // Set up resize observer for canvas
        this.setupCanvasResizeObserver();
        
        // Set up window resize listener
        this.setupWindowResizeListener();
        
        // Initial scale calculation
        this.calculateAndApplyScale();
    },

    // Set up ResizeObserver to watch canvas size changes
    setupCanvasResizeObserver() {
        if (typeof ResizeObserver !== 'undefined') {
            this.canvasResizeObserver = new ResizeObserver((entries) => {
                for (let entry of entries) {
                    if (entry.target === this.canvas) {
                        // Debounce resize events
                        clearTimeout(this.resizeTimeout);
                        this.resizeTimeout = setTimeout(() => {
                            this.calculateAndApplyScale();
                        }, 100);
                    }
                }
            });
            this.canvasResizeObserver.observe(this.canvas);
        }
    },

    // Set up window resize listener as fallback
    setupWindowResizeListener() {
        this.windowResizeHandler = () => {
            clearTimeout(this.resizeTimeout);
            this.resizeTimeout = setTimeout(() => {
                this.calculateAndApplyScale();
            }, 150);
        };
        window.addEventListener('resize', this.windowResizeHandler);
        window.addEventListener('orientationchange', this.windowResizeHandler);
    },

    // Calculate current scale factor based on canvas size
    calculateAndApplyScale() {
        const canvasRect = this.canvas.getBoundingClientRect();
        const currentCanvasWidth = canvasRect.width;
        
        // Calculate scale factor
        const newScale = currentCanvasWidth / this.baseCanvasWidth;
        
        // Determine if we're in responsive mode (scaled down)
        const wasResponsiveMode = this.isResponsiveMode;
        this.isResponsiveMode = newScale < 0.95; // Consider responsive if scaled down by more than 5%
        
        // Only update if scale changed significantly or responsive mode changed
        if (Math.abs(newScale - this.currentScale) > 0.05 || wasResponsiveMode !== this.isResponsiveMode) {
            this.currentScale = newScale;
            this.applyScaleToElements();
            
            // Update canvas data attribute for CSS targeting
            if (this.isResponsiveMode) {
                this.canvas.setAttribute('data-mobile-scale', this.currentScale.toFixed(3));
            } else {
                this.canvas.removeAttribute('data-mobile-scale');
            }
            
            console.log(`Responsive scaling: ${(this.currentScale * 100).toFixed(1)}% (${this.isResponsiveMode ? 'mobile' : 'desktop'} mode)`);
        }
    },

    // Apply scale to all draggable elements
    applyScaleToElements() {
        const elements = this.canvas.querySelectorAll('.cf7-draggable-text, .cf7-draggable-image');
        
        elements.forEach(element => {
            this.scaleElement(element);
        });
    },

    // Scale individual element with high-precision coordinate mapping
    scaleElement(element) {
        if (!element) return;

        // Get original dimensions if not stored
        if (!element.dataset.originalWidth) {
            this.storeOriginalDimensions(element);
        }

        const originalWidth = parseFloat(element.dataset.originalWidth);
        const originalHeight = parseFloat(element.dataset.originalHeight);
        const originalLeft = parseFloat(element.dataset.originalLeft);
        const originalTop = parseFloat(element.dataset.originalTop);
        const originalFontSize = parseFloat(element.dataset.originalFontSize) || 16;

        // Store the original coordinates in a normalized format (0-1 range)
        if (!element.dataset.normalizedLeft) {
            element.dataset.normalizedLeft = (originalLeft / this.baseCanvasWidth).toString();
            element.dataset.normalizedTop = (originalTop / this.baseCanvasHeight).toString();
            element.dataset.normalizedWidth = (originalWidth / this.baseCanvasWidth).toString();
            element.dataset.normalizedHeight = (originalHeight / this.baseCanvasHeight).toString();
        }

        // Apply scaling using normalized coordinates for consistency
        if (this.isResponsiveMode) {
            const currentCanvasRect = this.canvas.getBoundingClientRect();
            const currentCanvasWidth = currentCanvasRect.width;
            const currentCanvasHeight = currentCanvasRect.height;

            // Calculate positions based on normalized coordinates
            const normalizedLeft = parseFloat(element.dataset.normalizedLeft);
            const normalizedTop = parseFloat(element.dataset.normalizedTop);
            const normalizedWidth = parseFloat(element.dataset.normalizedWidth);
            const normalizedHeight = parseFloat(element.dataset.normalizedHeight);

            // Apply normalized positioning for perfect consistency
            element.style.left = (normalizedLeft * currentCanvasWidth) + 'px';
            element.style.top = (normalizedTop * currentCanvasHeight) + 'px';
            element.style.width = (normalizedWidth * currentCanvasWidth) + 'px';
            element.style.height = (normalizedHeight * currentCanvasHeight) + 'px';

            // Scale font size proportionally but maintain readability
            if (element.classList.contains('cf7-draggable-text')) {
                const editableContent = element.querySelector('.cf7-editable-content');
                if (editableContent) {
                    const scaledFontSize = Math.max(10, originalFontSize * this.currentScale);
                    editableContent.style.fontSize = scaledFontSize + 'px';
                }
            }

            // Add responsive class for additional CSS targeting
            element.classList.add('cf7-responsive-scaled');
        } else {
            // Reset to original dimensions using stored values
            element.style.width = originalWidth + 'px';
            element.style.height = originalHeight + 'px';
            element.style.left = originalLeft + 'px';
            element.style.top = originalTop + 'px';

            // Reset font size for text elements
            if (element.classList.contains('cf7-draggable-text')) {
                const editableContent = element.querySelector('.cf7-editable-content');
                if (editableContent) {
                    editableContent.style.fontSize = originalFontSize + 'px';
                }
            }

            element.classList.remove('cf7-responsive-scaled');
        }
    },

    // Store original dimensions when element is created or first scaled
    storeOriginalDimensions(element) {
        const computedStyle = window.getComputedStyle(element);
        const rect = element.getBoundingClientRect();
        
        element.dataset.originalWidth = parseFloat(computedStyle.width) || rect.width;
        element.dataset.originalHeight = parseFloat(computedStyle.height) || rect.height;
        element.dataset.originalLeft = parseFloat(computedStyle.left) || 0;
        element.dataset.originalTop = parseFloat(computedStyle.top) || 0;
        
        // Store original font size for text elements
        if (element.classList.contains('cf7-draggable-text')) {
            const editableContent = element.querySelector('.cf7-editable-content');
            if (editableContent) {
                const fontSize = window.getComputedStyle(editableContent).fontSize;
                element.dataset.originalFontSize = parseFloat(fontSize) || 16;
            }
        }
    },

    // Update original dimensions when element is resized or moved
    updateOriginalDimensions(element) {
        if (!this.isResponsiveMode) {
            // Only update original dimensions when not in responsive mode
            this.storeOriginalDimensions(element);
        }
    },

    // Handle new elements added to canvas
    onElementAdded(element) {
        // Store original dimensions immediately
        setTimeout(() => {
            this.storeOriginalDimensions(element);
            this.scaleElement(element);
        }, 10);
    },

    // Get export dimensions that maintain quality across all devices
    getExportDimensions() {
        // Always use the base canvas dimensions for export, regardless of current scale
        const devicePixelRatio = window.devicePixelRatio || 1;

        return {
            width: this.baseCanvasWidth,
            height: this.baseCanvasHeight,
            // Use higher pixel ratio for better quality on high-DPI screens
            pixelRatio: Math.max(2, devicePixelRatio),
            // Canvas dimensions for html-to-image
            canvasWidth: this.baseCanvasWidth * Math.max(2, devicePixelRatio),
            canvasHeight: this.baseCanvasHeight * Math.max(2, devicePixelRatio)
        };
    },

    // Temporarily restore elements to original positions for export
    prepareElementsForExport() {
        const elements = this.canvas.querySelectorAll('.cf7-draggable-text, .cf7-draggable-image');
        const originalStates = [];

        elements.forEach(element => {
            // Store current state
            originalStates.push({
                element: element,
                left: element.style.left,
                top: element.style.top,
                width: element.style.width,
                height: element.style.height,
                fontSize: element.querySelector('.cf7-editable-content')?.style.fontSize || ''
            });

            // Restore to original dimensions for export
            if (element.dataset.originalWidth) {
                element.style.left = element.dataset.originalLeft + 'px';
                element.style.top = element.dataset.originalTop + 'px';
                element.style.width = element.dataset.originalWidth + 'px';
                element.style.height = element.dataset.originalHeight + 'px';

                // Restore original font size
                if (element.classList.contains('cf7-draggable-text')) {
                    const editableContent = element.querySelector('.cf7-editable-content');
                    if (editableContent && element.dataset.originalFontSize) {
                        editableContent.style.fontSize = element.dataset.originalFontSize + 'px';
                    }
                }
            }
        });

        return originalStates;
    },

    // Restore elements to their scaled state after export
    restoreElementsAfterExport(originalStates) {
        originalStates.forEach(state => {
            state.element.style.left = state.left;
            state.element.style.top = state.top;
            state.element.style.width = state.width;
            state.element.style.height = state.height;

            if (state.fontSize) {
                const editableContent = state.element.querySelector('.cf7-editable-content');
                if (editableContent) {
                    editableContent.style.fontSize = state.fontSize;
                }
            }
        });
    },

    // Clean up responsive scaling
    destroyResponsiveScaling() {
        if (this.canvasResizeObserver) {
            this.canvasResizeObserver.disconnect();
        }
        if (this.windowResizeHandler) {
            window.removeEventListener('resize', this.windowResizeHandler);
            window.removeEventListener('orientationchange', this.windowResizeHandler);
        }
        clearTimeout(this.resizeTimeout);
    }
});
