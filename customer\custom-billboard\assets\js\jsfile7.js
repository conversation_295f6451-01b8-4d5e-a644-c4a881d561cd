// ========================================
// DRAG AND DROP FUNCTIONALITY
// ========================================

// Extend CF7TextEditor class with drag and drop methods
Object.assign(CF7TextEditor.prototype, {
    handleMouseDown(e, element) {
            // CF7 Pattern: Skip drag for control elements
            if (e.target.classList.contains('cf7-delete-btn')) return;
            if (e.target.classList.contains('cf7-resize-handle')) return;

            // CF7 Pattern: Skip drag if clicking on editable content that's focused
            if (e.target.classList.contains('cf7-editable-content')) {
                const editableContent = e.target;
                // Only allow drag if not actively editing
                if (document.activeElement === editableContent || element.classList.contains('cf7-editing')) {
                    return; // Let text editing take precedence
                }
            }

            // Check if clicking on resize area for images
            if (element.classList.contains('cf7-draggable-image')) {
                const rect = element.getBoundingClientRect();
                const isResizing = (
                    e.clientX > rect.right - 20 &&
                    e.clientY > rect.bottom - 20
                );

                if (isResizing) {
                    // Let CSS handle the resize, don't start dragging
                    return;
                }
            }

            // CF7 Pattern: Initialize drag data
            this.dragData.isDragging = true;
            this.dragData.startX = e.clientX;
            this.dragData.startY = e.clientY;
            this.dragData.elementX = parseInt(element.style.left) || 0;
            this.dragData.elementY = parseInt(element.style.top) || 0;
            this.dragData.element = element;

            this.selectElement(element);
            e.preventDefault();
        },

        handleMouseMove(e) {
            if (!this.dragData.isDragging || !this.dragData.element) return;

            const deltaX = e.clientX - this.dragData.startX;
            const deltaY = e.clientY - this.dragData.startY;

            const newX = this.dragData.elementX + deltaX;
            const newY = this.dragData.elementY + deltaY;

            // Keep element within canvas bounds
            const canvasRect = this.canvas.getBoundingClientRect();
            const elementRect = this.dragData.element.getBoundingClientRect();

            const maxX = canvasRect.width - elementRect.width;
            const maxY = canvasRect.height - elementRect.height;

            this.dragData.element.style.left = Math.max(0, Math.min(newX, maxX)) + 'px';
            this.dragData.element.style.top = Math.max(0, Math.min(newY, maxY)) + 'px';
        },

        handleMouseUp() {
            this.dragData.isDragging = false;
            this.dragData.element = null;
        },

        handleResizeDown(e, element, direction) {
            e.stopPropagation();
            e.preventDefault();

            // Store original values when resize starts
            this.resizeData.isResizing = true;
            this.resizeData.originalMouseX = e.clientX;
            this.resizeData.originalMouseY = e.clientY;
            this.resizeData.originalWidth = parseInt(element.style.width) || element.offsetWidth;
            this.resizeData.originalHeight = parseInt(element.style.height) || element.offsetHeight;
            this.resizeData.originalX = parseInt(element.style.left) || 0;
            this.resizeData.originalY = parseInt(element.style.top) || 0;
            this.resizeData.element = element;
            this.resizeData.handle = direction;

            this.selectElement(element);
        },

        handleResizeMove(e) {
            if (!this.resizeData.isResizing || !this.resizeData.element) return;

            // Calculate mouse movement delta from original position
            const deltaX = e.clientX - this.resizeData.originalMouseX;
            const deltaY = e.clientY - this.resizeData.originalMouseY;

            let newWidth, newHeight, newLeft, newTop;

            // Use the formulas from the research - calculate based on original values + delta
            switch (this.resizeData.handle) {
                case 'se': // Southeast (bottom-right)
                    newWidth = Math.max(50, this.resizeData.originalWidth + deltaX);
                    newHeight = Math.max(20, this.resizeData.originalHeight + deltaY);
                    newLeft = this.resizeData.originalX;
                    newTop = this.resizeData.originalY;
                    break;

                case 'sw': // Southwest (bottom-left)
                    newWidth = Math.max(50, this.resizeData.originalWidth - deltaX);
                    newHeight = Math.max(20, this.resizeData.originalHeight + deltaY);
                    newLeft = this.resizeData.originalX + deltaX;
                    newTop = this.resizeData.originalY;

                    // Prevent width from going below minimum
                    if (newWidth === 50) {
                        newLeft = this.resizeData.originalX + this.resizeData.originalWidth - 50;
                    }
                    break;

                case 'ne': // Northeast (top-right)
                    newWidth = Math.max(50, this.resizeData.originalWidth + deltaX);
                    newHeight = Math.max(20, this.resizeData.originalHeight - deltaY);
                    newLeft = this.resizeData.originalX;
                    newTop = this.resizeData.originalY + deltaY;

                    // Prevent height from going below minimum
                    if (newHeight === 20) {
                        newTop = this.resizeData.originalY + this.resizeData.originalHeight - 20;
                    }
                    break;

                case 'nw': // Northwest (top-left)
                    newWidth = Math.max(50, this.resizeData.originalWidth - deltaX);
                    newHeight = Math.max(20, this.resizeData.originalHeight - deltaY);
                    newLeft = this.resizeData.originalX + deltaX;
                    newTop = this.resizeData.originalY + deltaY;

                    // Prevent dimensions from going below minimum
                    if (newWidth === 50) {
                        newLeft = this.resizeData.originalX + this.resizeData.originalWidth - 50;
                    }
                    if (newHeight === 20) {
                        newTop = this.resizeData.originalY + this.resizeData.originalHeight - 20;
                    }
                    break;
            }

            // Keep element within canvas bounds
            const canvasRect = this.canvas.getBoundingClientRect();
            const maxX = canvasRect.width - newWidth;
            const maxY = canvasRect.height - newHeight;

            newLeft = Math.max(0, Math.min(newLeft, maxX));
            newTop = Math.max(0, Math.min(newTop, maxY));

            // Apply the calculated values
            this.resizeData.element.style.width = newWidth + 'px';
            this.resizeData.element.style.height = newHeight + 'px';
            this.resizeData.element.style.left = newLeft + 'px';
            this.resizeData.element.style.top = newTop + 'px';
        },

        handleResizeUp() {
            this.resizeData.isResizing = false;
            this.resizeData.element = null;
            this.resizeData.handle = null;
        },

        selectElement(element) {
            this.deselectAll();
            element.classList.add('cf7-selected');
            this.selectedElement = element;
            this.updateFontControlsFromElement(element);

            // Enable font controls if text element is selected - CF7 Pattern
            if (element.classList.contains('cf7-draggable-text')) {
                this.enableFontControls();
            }
        },

        deselectAll() {
            const selected = this.container.querySelectorAll('.cf7-selected');
            selected.forEach(el => el.classList.remove('cf7-selected'));
            this.selectedElement = null;
            this.resetFontControls();
        },

        updateFontControlsFromElement(element) {
            if (!element.classList.contains('cf7-draggable-text')) {
                // If not a text element, disable font controls
                this.disableFontControls();
                return;
            }

            // Enable font controls for text elements - CF7 Pattern
            this.enableFontControls();

            // Get styles from editable content if available, otherwise from element
            const editableContent = element.querySelector('.cf7-editable-content');
            const targetElement = editableContent || element;
            const computedStyle = window.getComputedStyle(targetElement);

            // Update font family
            if (this.fontControls.fontFamily) {
                this.fontControls.fontFamily.value = computedStyle.fontFamily;
            }

            // Update font size
            if (this.fontControls.fontSize) {
                this.fontControls.fontSize.value = parseInt(computedStyle.fontSize);
            }

            // Update bold button
            if (this.fontControls.bold) {
                const isBold = computedStyle.fontWeight === 'bold' || parseInt(computedStyle.fontWeight) >= 700;
                this.fontControls.bold.classList.toggle('cf7-active', isBold);
            }

            // Update italic button
            if (this.fontControls.italic) {
                const isItalic = computedStyle.fontStyle === 'italic';
                this.fontControls.italic.classList.toggle('cf7-active', isItalic);
            }

            // Update color picker
            if (this.fontControls.color) {
                const color = computedStyle.color;
                // Convert RGB to hex for color picker
                const hexColor = this.rgbToHex(color);
                this.fontControls.color.value = hexColor;
            }

            // Update text alignment buttons
            const currentAlignment = computedStyle.textAlign || 'left';
            // Clear all alignment button active states
            if (this.fontControls.alignLeft) this.fontControls.alignLeft.classList.remove('cf7-active');
            if (this.fontControls.alignCenter) this.fontControls.alignCenter.classList.remove('cf7-active');
            if (this.fontControls.alignRight) this.fontControls.alignRight.classList.remove('cf7-active');
            if (this.fontControls.alignJustify) this.fontControls.alignJustify.classList.remove('cf7-active');

            // Set the current alignment button as active
            if (currentAlignment === 'left' && this.fontControls.alignLeft) {
                this.fontControls.alignLeft.classList.add('cf7-active');
            } else if (currentAlignment === 'center' && this.fontControls.alignCenter) {
                this.fontControls.alignCenter.classList.add('cf7-active');
            } else if (currentAlignment === 'right' && this.fontControls.alignRight) {
                this.fontControls.alignRight.classList.add('cf7-active');
            } else if (currentAlignment === 'justify' && this.fontControls.alignJustify) {
                this.fontControls.alignJustify.classList.add('cf7-active');
            }

            // Update text shadow controls
            this.updateTextShadowControls(element, computedStyle);
        },

        updateTextShadowControls(element, computedStyle) {
            // Check editable content for text shadow first
            const editableContent = element.querySelector('.cf7-editable-content');
            const targetElement = editableContent || element;
            const targetStyle = editableContent ? window.getComputedStyle(editableContent) : computedStyle;
            const textShadow = targetStyle.textShadow || targetElement.style.textShadow;

            const shadowToggle = this.container.querySelector('.cf7-btn-shadow');
            const shadowColorPicker = this.container.querySelector('.cf7-color-picker[data-shadow="color"]');
            const shadowBlurInput = this.container.querySelector('.cf7-input-shadow-blur');
            const shadowOffsetXInput = this.container.querySelector('.cf7-input-shadow-offset[data-axis="x"]');
            const shadowOffsetYInput = this.container.querySelector('.cf7-input-shadow-offset[data-axis="y"]');
            const shadowOpacitySlider = this.container.querySelector('.cf7-range-slider[data-shadow-opacity="true"]');

            // Check if element has shadow applied (including stored config)
            const hasStoredShadow = element.shadowConfig && element.shadowConfig.applied !== false;
            const hasVisibleShadow = textShadow && textShadow !== 'none';

            if (hasVisibleShadow || hasStoredShadow) {
                // Enhanced regex to match both rgb and rgba patterns
                const shadowMatch = textShadow ? textShadow.match(/(-?\d+(?:\.\d+)?)px\s+(-?\d+(?:\.\d+)?)px\s+(-?\d+(?:\.\d+)?)px\s+(.+)/) : null;

                if ((shadowMatch || hasStoredShadow) && shadowToggle) {
                    // Element has shadow - set button to active
                    shadowToggle.classList.add('cf7-active');

                    if (shadowMatch) {
                        const [, offsetX, offsetY, blur, color] = shadowMatch;

                        // Extract opacity from rgba if present
                        let opacity = '100';
                        if (color.includes('rgba')) {
                            const rgbaMatch = color.match(/rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*([\d.]+)\s*\)/);
                            if (rgbaMatch) {
                                opacity = Math.round(parseFloat(rgbaMatch[1]) * 100).toString();
                            }
                        }

                        // Update input controls
                        if (shadowOffsetXInput) shadowOffsetXInput.value = offsetX;
                        if (shadowOffsetYInput) shadowOffsetYInput.value = offsetY;
                        if (shadowBlurInput) shadowBlurInput.value = blur;
                        if (shadowColorPicker) shadowColorPicker.value = this.rgbToHex(color.trim());

                        // Update opacity slider
                        if (shadowOpacitySlider) {
                            shadowOpacitySlider.value = opacity;
                            const valueDisplay = document.getElementById('cf7-opacity-value');
                            if (valueDisplay) valueDisplay.textContent = opacity + '%';
                        }

                        // Store shadow config on element
                        element.shadowConfig = {
                            color: this.rgbToHex(color.trim()),
                            blur: blur,
                            offsetX: offsetX,
                            offsetY: offsetY,
                            opacity: opacity,
                            applied: true
                        };
                    } else if (hasStoredShadow && element.shadowConfig) {
                        // Use stored config to update controls
                        const config = element.shadowConfig;
                        if (shadowOffsetXInput) shadowOffsetXInput.value = config.offsetX || '2';
                        if (shadowOffsetYInput) shadowOffsetYInput.value = config.offsetY || '2';
                        if (shadowBlurInput) shadowBlurInput.value = config.blur || '2';
                        if (shadowColorPicker) shadowColorPicker.value = config.color || '#000000';
                        if (shadowOpacitySlider) {
                            shadowOpacitySlider.value = config.opacity || '100';
                            const valueDisplay = document.getElementById('cf7-opacity-value');
                            if (valueDisplay) valueDisplay.textContent = (config.opacity || '100') + '%';
                        }
                    }
                }
            } else {
                // Element has no shadow - set button to inactive (like bold/italic)
                if (shadowToggle) shadowToggle.classList.remove('cf7-active');

                // Reset to default values
                if (shadowOffsetXInput) shadowOffsetXInput.value = '2';
                if (shadowOffsetYInput) shadowOffsetYInput.value = '2';
                if (shadowBlurInput) shadowBlurInput.value = '2';
                if (shadowColorPicker) shadowColorPicker.value = '#000000';

                // Reset opacity slider
                if (shadowOpacitySlider) {
                    shadowOpacitySlider.value = '100';
                    const valueDisplay = document.getElementById('cf7-opacity-value');
                    if (valueDisplay) valueDisplay.textContent = '100%';
                }
            }
        },

        resetFontControls() {
            if (this.fontControls.fontFamily) this.fontControls.fontFamily.value = 'Arial, sans-serif';
            if (this.fontControls.fontSize) this.fontControls.fontSize.value = '16';
            if (this.fontControls.bold) this.fontControls.bold.classList.remove('cf7-active');
            if (this.fontControls.italic) this.fontControls.italic.classList.remove('cf7-active');
            if (this.fontControls.color) this.fontControls.color.value = '#000000';

            // Reset text alignment buttons
            if (this.fontControls.alignLeft) this.fontControls.alignLeft.classList.remove('cf7-active');
            if (this.fontControls.alignCenter) this.fontControls.alignCenter.classList.remove('cf7-active');
            if (this.fontControls.alignRight) this.fontControls.alignRight.classList.remove('cf7-active');
            if (this.fontControls.alignJustify) this.fontControls.alignJustify.classList.remove('cf7-active');

            // Reset text shadow controls to defaults - CF7 Compatible
            // Note: Don't reset shadow button state here - it should be managed per element
            const shadowColorPicker = this.container.querySelector('.cf7-color-picker[data-shadow="color"]');
            const shadowBlurInput = this.container.querySelector('.cf7-input-shadow-blur');
            const shadowOffsetXInput = this.container.querySelector('.cf7-input-shadow-offset[data-axis="x"]');
            const shadowOffsetYInput = this.container.querySelector('.cf7-input-shadow-offset[data-axis="y"]');

            if (shadowColorPicker) shadowColorPicker.value = '#000000';
            if (shadowBlurInput) shadowBlurInput.value = '2';
            if (shadowOffsetXInput) shadowOffsetXInput.value = '2';
            if (shadowOffsetYInput) shadowOffsetYInput.value = '2';

            // Only reset shadow button when no text is selected (disable state)
            if (this.fontControls.textShadow) this.fontControls.textShadow.classList.remove('cf7-active');

            // Disable all font controls when no text is selected - CF7 Pattern
            this.disableFontControls();
        },

        // CF7 Compatible: Enable font controls when text is selected
        enableFontControls() {
            // Enable font family dropdown (custom font preview dropdown)
            if (this.fontControls.fontFamily) {
                // Check if it's the new custom font preview dropdown
                if (this.fontControls.fontFamily.classList.contains('cf7-font-preview-dropdown')) {
                    this.fontControls.fontFamily.classList.remove('cf7-disabled');
                    const button = this.fontControls.fontFamily.querySelector('.cf7-font-preview-button');
                    if (button) {
                        button.disabled = false;
                    }
                } else {
                    // Handle regular select dropdown
                    this.fontControls.fontFamily.disabled = false;
                    this.fontControls.fontFamily.classList.remove('cf7-disabled');
                }
            }

            // Enable font size input
            if (this.fontControls.fontSize) {
                this.fontControls.fontSize.disabled = false;
                this.fontControls.fontSize.classList.remove('cf7-disabled');
            }

            // Enable style buttons
            if (this.fontControls.bold) {
                this.fontControls.bold.disabled = false;
                this.fontControls.bold.classList.remove('cf7-disabled');
            }
            if (this.fontControls.italic) {
                this.fontControls.italic.disabled = false;
                this.fontControls.italic.classList.remove('cf7-disabled');
            }

            // Enable text alignment buttons
            if (this.fontControls.alignLeft) {
                this.fontControls.alignLeft.disabled = false;
                this.fontControls.alignLeft.classList.remove('cf7-disabled');
            }
            if (this.fontControls.alignCenter) {
                this.fontControls.alignCenter.disabled = false;
                this.fontControls.alignCenter.classList.remove('cf7-disabled');
            }
            if (this.fontControls.alignRight) {
                this.fontControls.alignRight.disabled = false;
                this.fontControls.alignRight.classList.remove('cf7-disabled');
            }
            if (this.fontControls.alignJustify) {
                this.fontControls.alignJustify.disabled = false;
                this.fontControls.alignJustify.classList.remove('cf7-disabled');
            }

            // Enable color picker
            if (this.fontControls.color) {
                this.fontControls.color.disabled = false;
                this.fontControls.color.classList.remove('cf7-disabled');
            }

            // Enable text shadow controls
            if (this.fontControls.textShadow) {
                this.fontControls.textShadow.disabled = false;
                this.fontControls.textShadow.classList.remove('cf7-disabled');
            }

            // Enable shadow controls
            const shadowColorPicker = this.container.querySelector('.cf7-color-picker[data-shadow="color"]');
            const shadowBlurInput = this.container.querySelector('.cf7-input-shadow-blur');
            const shadowOffsetXInput = this.container.querySelector('.cf7-input-shadow-offset[data-axis="x"]');
            const shadowOffsetYInput = this.container.querySelector('.cf7-input-shadow-offset[data-axis="y"]');
            const shadowOpacitySlider = this.container.querySelector('.cf7-range-slider[data-shadow-opacity="true"]');

            if (shadowColorPicker) {
                shadowColorPicker.disabled = false;
                shadowColorPicker.classList.remove('cf7-disabled');
            }
            if (shadowBlurInput) {
                shadowBlurInput.disabled = false;
                shadowBlurInput.classList.remove('cf7-disabled');
            }
            if (shadowOffsetXInput) {
                shadowOffsetXInput.disabled = false;
                shadowOffsetXInput.classList.remove('cf7-disabled');
            }
            if (shadowOffsetYInput) {
                shadowOffsetYInput.disabled = false;
                shadowOffsetYInput.classList.remove('cf7-disabled');
            }
            if (shadowOpacitySlider) {
                shadowOpacitySlider.disabled = false;
                shadowOpacitySlider.classList.remove('cf7-disabled');
            }
        },

        // CF7 Compatible: Disable font controls when no text is selected
        disableFontControls() {
            // Disable font family dropdown (custom font preview dropdown)
            if (this.fontControls.fontFamily) {
                // Check if it's the new custom font preview dropdown
                if (this.fontControls.fontFamily.classList.contains('cf7-font-preview-dropdown')) {
                    this.fontControls.fontFamily.classList.add('cf7-disabled');
                    const button = this.fontControls.fontFamily.querySelector('.cf7-font-preview-button');
                    if (button) {
                        button.disabled = true;
                    }
                    // Also close the dropdown if it's open
                    const dropdownList = this.fontControls.fontFamily.querySelector('.cf7-font-preview-list');
                    if (dropdownList) {
                        dropdownList.classList.add('cf7-hidden');
                        button.setAttribute('aria-expanded', 'false');
                    }
                } else {
                    // Handle regular select dropdown
                    this.fontControls.fontFamily.disabled = true;
                    this.fontControls.fontFamily.classList.add('cf7-disabled');
                }
            }

            // Disable font size input
            if (this.fontControls.fontSize) {
                this.fontControls.fontSize.disabled = true;
                this.fontControls.fontSize.classList.add('cf7-disabled');
            }

            // Disable style buttons
            if (this.fontControls.bold) {
                this.fontControls.bold.disabled = true;
                this.fontControls.bold.classList.add('cf7-disabled');
            }
            if (this.fontControls.italic) {
                this.fontControls.italic.disabled = true;
                this.fontControls.italic.classList.add('cf7-disabled');
            }

            // Disable text alignment buttons
            if (this.fontControls.alignLeft) {
                this.fontControls.alignLeft.disabled = true;
                this.fontControls.alignLeft.classList.add('cf7-disabled');
            }
            if (this.fontControls.alignCenter) {
                this.fontControls.alignCenter.disabled = true;
                this.fontControls.alignCenter.classList.add('cf7-disabled');
            }
            if (this.fontControls.alignRight) {
                this.fontControls.alignRight.disabled = true;
                this.fontControls.alignRight.classList.add('cf7-disabled');
            }
            if (this.fontControls.alignJustify) {
                this.fontControls.alignJustify.disabled = true;
                this.fontControls.alignJustify.classList.add('cf7-disabled');
            }

            // Disable color picker
            if (this.fontControls.color) {
                this.fontControls.color.disabled = true;
                this.fontControls.color.classList.add('cf7-disabled');
            }

            // Disable text shadow controls
            if (this.fontControls.textShadow) {
                this.fontControls.textShadow.disabled = true;
                this.fontControls.textShadow.classList.add('cf7-disabled');
            }

            // Disable shadow controls
            const shadowColorPicker = this.container.querySelector('.cf7-color-picker[data-shadow="color"]');
            const shadowBlurInput = this.container.querySelector('.cf7-input-shadow-blur');
            const shadowOffsetXInput = this.container.querySelector('.cf7-input-shadow-offset[data-axis="x"]');
            const shadowOffsetYInput = this.container.querySelector('.cf7-input-shadow-offset[data-axis="y"]');
            const shadowOpacitySlider = this.container.querySelector('.cf7-range-slider[data-shadow-opacity="true"]');

            if (shadowColorPicker) {
                shadowColorPicker.disabled = true;
                shadowColorPicker.classList.add('cf7-disabled');
            }
            if (shadowBlurInput) {
                shadowBlurInput.disabled = true;
                shadowBlurInput.classList.add('cf7-disabled');
            }
            if (shadowOffsetXInput) {
                shadowOffsetXInput.disabled = true;
                shadowOffsetXInput.classList.add('cf7-disabled');
            }
            if (shadowOffsetYInput) {
                shadowOffsetYInput.disabled = true;
                shadowOffsetYInput.classList.add('cf7-disabled');
            }
            if (shadowOpacitySlider) {
                shadowOpacitySlider.disabled = true;
                shadowOpacitySlider.classList.add('cf7-disabled');
            }
        },

        rgbToHex(rgb) {
            // Handle hex colors that are already in the right format
            if (rgb.startsWith('#')) return rgb;

            // Handle rgb() format
            const result = rgb.match(/\d+/g);
            if (result && result.length >= 3) {
                const r = parseInt(result[0]);
                const g = parseInt(result[1]);
                const b = parseInt(result[2]);
                return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
            }
            return '#000000'; // Default fallback
        }
});