<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Billboard Designer - Borges Media</title>

    <!-- Google Fonts - Popular and Accessible Font Choices -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;500;600;700&family=Lato:wght@300;400;700&family=Montserrat:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&family=Merriweather:wght@300;400;700&family=Source+Sans+Pro:wght@300;400;600;700&family=Nunito:wght@300;400;500;600;700&family=Raleway:wght@300;400;500;600;700&family=Oswald:wght@300;400;500;600;700&family=Dancing+Script:wght@400;500;600;700&family=Pacifico&family=Lobster&display=swap" rel="stylesheet">

    <!-- Font Awesome 6 - For Text Alignment Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Custom Billboard Styles -->
    <link rel="stylesheet" href="assets/css/custom-styles.css">
    <link rel="stylesheet" href="assets/css/custom-styles-body.css">
</head>

<body>
    <!-- Fixed Header with Back Button -->
    <div class="cf7-fixed-header">
        <div class="cf7-header-content">
            <div class="cf7-header-brand">
                <div class="cf7-header-logo">BM</div>
                <div>
                    <h1 class="cf7-header-title">Custom Billboard Designer</h1>
                    <p class="cf7-header-subtitle">Create your perfect billboard design</p>
                </div>
            </div>
            <div class="cf7-header-nav">
                <button onclick="goBack()" class="cf7-btn-back" style="background: #0073aa; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; display: flex; align-items: center; gap: 8px;">
                    <i class="fas fa-arrow-left"></i>
                    Back to Home
                </button>
            </div>
        </div>
    </div>

    <!-- CF7 Text Editor Implementation -->
    <!-- Contact Form 7 Style Text Editor -->
    <!-- Usage: [cf7-text-editor canvas-width:800 canvas-height:600] -->

    <div class="cf7-text-editor-container">
        <!-- Canvas Area - Billboard 10x20 feet (20 wide x 10 tall = 2:1 aspect ratio) -->
        <div class="cf7-canvas" id="cf7-canvas" data-width="800" data-height="400">
            <div class="cf7-canvas-background" id="cf7-canvas-bg"></div>
            <div class="cf7-elements-container" id="cf7-elements"></div>
        </div>

        <!-- Toolbar -->
        <div class="cf7-toolbar">
            <!-- Background Image Selector and Action Buttons -->
            <div class="cf7-background-controls">
                <button class="cf7-btn-background-modal" id="cf7-bg-modal-trigger"
                        aria-label="Open background image selector">
                    Choose Background
                </button>
                <button class="cf7-btn-clear-bg" id="cf7-clear-bg"
                        aria-label="Clear background image">
                    Clear Background
                </button>

                <!-- Add Image Element -->
                [cf7-add-image class:cf7-btn-image text:"Add Image"]

                <!-- Clear Canvas -->
                [cf7-clear-canvas class:cf7-btn-clear text:"Clear All"]
            </div>

            <!-- Font Controls -->
            <div class="cf7-font-controls">

                <!-- Font Family Control -->
                <div class="cf7-control-group">
                <!-- Add Text Element - First in font controls -->
                [cf7-add-text class:cf7-btn-text text:"Add Text"]
                </div>

                <!-- Font Family Control -->
                <div class="cf7-control-group">
                    <label class="cf7-control-label">Font:</label>
                    [cf7-font-family class:cf7-select-font]
                </div>

                <!-- Font Size Control -->
                <div class="cf7-control-group">
                    <label class="cf7-control-label">Size:</label>
                    [cf7-font-size class:cf7-input-size value:"16"]
                </div>

                <!-- Font Style Controls -->
                <div class="cf7-control-group">
                    <label class="cf7-control-label">Style:</label>
                    <div class="cf7-style-buttons">
                        [cf7-font-bold class:cf7-btn-style text:"B"]
                        [cf7-font-italic class:cf7-btn-style text:"I"]
                    </div>
                </div>

                <!-- Text Alignment Controls -->
                <div class="cf7-control-group">
                    <label class="cf7-control-label">Align:</label>
                    <div class="cf7-style-buttons">
                        <span>[cf7-align-left class:cf7-btn-style text:"⬅"]</span>
                        <span>[cf7-align-center class:cf7-btn-style text:"⬌"]</span>
                        <span>[cf7-align-right class:cf7-btn-style text:"➡"]</span>
                        <span>[cf7-align-justify class:cf7-btn-style text:"≡"]</span>
                    </div>
                </div>

                <!-- Font Color Control -->
                <div class="cf7-control-group">
                    <label class="cf7-control-label">Color:</label>
                    [cf7-font-color class:cf7-color-picker value:"#000000"]
                </div>

                <!-- Text Shadow Controls -->
                <div class="cf7-control-group">
                    <label class="cf7-control-label">Shadow:</label>
                    <div class="cf7-shadow-controls">
                        [cf7-text-shadow-toggle class:cf7-btn-shadow text:"S"]
                        [cf7-shadow-color class:cf7-color-picker value:"#000000"]
                        [cf7-shadow-blur class:cf7-input-shadow-blur value:"2"]
                        [cf7-shadow-offset-x class:cf7-input-shadow-offset value:"2"]
                        [cf7-shadow-offset-y class:cf7-input-shadow-offset value:"2"]
                    </div>
                </div>

                <!-- Shadow Opacity Slider -->
                <div class="cf7-control-group">
                    <label class="cf7-control-label">Opacity:</label>
                    <div class="cf7-slider-container">
                        [cf7-shadow-opacity-slider class:cf7-range-slider min:0 max:100 value:100]
                        <span class="cf7-slider-value" id="cf7-opacity-value">100%</span>
                    </div>
                </div>
            </div>

            <!-- Export and Payment Controls -->
            <div class="cf7-export-controls">
                <label class="cf7-control-label">Export & Order:</label>
                <div class="cf7-export-buttons">
                    <button type="button" class="cf7-btn-download" id="cf7-export-image" onclick="exportBillboardImage()">
                        <i class="fas fa-download"></i>
                        Download Image
                    </button>
                    <button type="button" class="cf7-btn-export" id="cf7-proceed-payment" onclick="openCheckoutDialog()">
                        <i class="fas fa-credit-card"></i>
                        Proceed to Payment
                    </button>
                </div>
            </div>
        </div>

        <!-- Background Image Modal -->
        <dialog class="cf7-background-modal" id="cf7-bg-modal" aria-labelledby="cf7-modal-title" aria-describedby="cf7-modal-desc">
            <div class="cf7-modal-content">
                <header class="cf7-modal-header">
                    <h2 id="cf7-modal-title" class="cf7-modal-title">Choose Background Image</h2>
                    <p id="cf7-modal-desc" class="cf7-modal-description">Select a category and image for your billboard background</p>
                    <button class="cf7-modal-close" id="cf7-modal-close" aria-label="Close background selector">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </header>

                <div class="cf7-modal-body">
                    <div class="cf7-modal-step cf7-step-category" id="cf7-step-category">
                        <h3 class="cf7-step-title">Step 1: Select Category</h3>
                        <div class="cf7-category-grid" id="cf7-category-grid" role="radiogroup" aria-labelledby="cf7-step-title">
                            <!-- Categories will be populated by JavaScript -->
                        </div>
                    </div>

                    <div class="cf7-modal-step cf7-step-template" id="cf7-step-template" style="display: none;">
                        <h3 class="cf7-step-title">Step 2: Select Image</h3>
                        <button class="cf7-back-btn" id="cf7-back-to-categories" aria-label="Back to categories">
                            ← Back to Categories
                        </button>
                        <div class="cf7-template-grid" id="cf7-template-grid" role="radiogroup" aria-labelledby="cf7-step-title">
                            <!-- Images will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <footer class="cf7-modal-footer">
                    <button class="cf7-btn-cancel" id="cf7-modal-cancel">Cancel</button>
                    <button class="cf7-btn-apply" id="cf7-modal-apply" disabled>Apply Background</button>
                </footer>
            </div>
        </dialog>

        <!-- Checkout Dialog - Using Background Modal Structure -->
        <dialog class="cf7-background-modal" id="checkoutDialog" aria-labelledby="checkout-modal-title" aria-describedby="checkout-modal-desc">
            <div class="cf7-modal-content">
                <header class="cf7-modal-header">
                    <h2 id="checkout-modal-title" class="cf7-modal-title">📋 Review Your Billboard Order</h2>
                    <p id="checkout-modal-desc" class="cf7-modal-description">Review your order details and agree to terms before proceeding to payment</p>
                    <button class="cf7-modal-close" id="checkout-modal-close" aria-label="Close checkout dialog" onclick="closeCheckoutDialog()">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </header>

                <div class="cf7-modal-body">
                    <div class="cf7-modal-step" id="checkout-step-content">
                        <!-- Order Summary -->
                        <div class="order-summary">
                            <h3 class="cf7-step-title">📊 Order Summary</h3>
                            <!-- Purpose row hidden as requested -->
                            <div class="summary-row">
                                <span class="summary-label">Billboard Location:</span>
                                <span class="summary-value" id="dialogSummaryLocation">Loading...</span>
                            </div>
                            <div class="summary-row">
                                <span class="summary-label">Run Dates:</span>
                                <span class="summary-value" id="dialogSummaryDates">Loading...</span>
                            </div>
                            <div class="summary-row">
                                <span class="summary-label">Duration:</span>
                                <span class="summary-value" id="dialogSummaryDuration">Loading...</span>
                            </div>
                            <div class="summary-row">
                                <span class="summary-label">Total Cost:</span>
                                <span class="summary-value" id="dialogSummaryCost">$0.00</span>
                            </div>
                        </div>

                        <!-- Terms and Email Options -->
                        <div class="checkbox-section">
                            <h3 class="cf7-step-title">📋 Agreement & Requirements</h3>

                            <div class="checkbox-item">
                                <input type="checkbox" name="dialog_terms_agreement" id="dialog_terms_agreement" required>
                                <label for="dialog_terms_agreement" class="checkbox-text">
                                    I agree to the terms and conditions
                                    <span class="terms-link" onclick="openTermsModal()" style="margin-left: 10px; color: #007cba; text-decoration: underline; cursor: pointer; font-weight: 500;">Click here to read full terms</span>
                                </label>
                            </div>

                            <div class="checkbox-item">
                                <input type="checkbox" name="dialog_content_compliance" id="dialog_content_compliance" required>
                                <label for="dialog_content_compliance" class="checkbox-text">
                                    I have NOT used photos of people smoking, drinking, hand symbols, showing too much skin.
                                </label>
                            </div>

                            <div class="checkbox-item">
                                <input type="checkbox" name="dialog_business_ad_compliance" id="dialog_business_ad_compliance" required>
                                <label for="dialog_business_ad_compliance" class="checkbox-text">
                                    My ad is not a business ad or promotional ad in any way.
                                </label>
                            </div>

                            <div class="checkbox-item">
                                <input type="checkbox" name="dialog_ad_preview_confirmation" id="dialog_ad_preview_confirmation" required>
                                <label for="dialog_ad_preview_confirmation" class="checkbox-text">
                                    The design above is exactly what my ad will look like. If it has to be changed, I will be charged $25.
                                </label>
                            </div>

                            <div class="checkbox-item">
                                <input type="checkbox" name="dialog_refund_policy_agreement" id="dialog_refund_policy_agreement" required>
                                <label for="dialog_refund_policy_agreement" class="checkbox-text">
                                    If your ad does not comply it will not play and you will not be refunded.
                                </label>
                            </div>

                            <div class="checkbox-item">
                                <input type="checkbox" name="dialog_email_copy" id="dialog_email_copy">
                                <label for="dialog_email_copy" class="checkbox-text">
                                    Email me a copy of this ad
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <footer class="cf7-modal-footer">
                    <button class="cf7-btn-cancel" id="checkout-modal-cancel" onclick="closeCheckoutDialog()">Cancel</button>
                    <button class="cf7-btn-apply" id="checkout-modal-proceed" onclick="proceedToPayment()">Proceed to Payment</button>
                </footer>
            </div>
        </dialog>

    </div>

    <!-- HTML-to-Image Library for Export Functionality -->
    <script src="https://cdn.jsdelivr.net/npm/html-to-image@1.11.13/dist/html-to-image.js"></script>

    <!-- Custom Billboard Functions - Modular Version -->
    <!-- Load all JavaScript modules in correct order -->

    <!-- 1. Core class definition and background templates -->
    <script src="assets/js/text-editor-core.js"></script>

    <!-- 2. Initialization methods -->
    <script src="assets/js/initialization.js"></script>

    <!-- 3. Background control setup -->
    <script src="assets/js/background-controls.js"></script>

    <!-- 4. Font control setup -->
    <script src="assets/js/font-controls.js"></script>

    <!-- 5. Shortcode conversion methods -->
    <script src="assets/js/shortcode-conversion.js"></script>

    <!-- 6. Element management methods -->
    <script src="assets/js/element-management.js"></script>

    <!-- 7. Drag and drop functionality -->
    <script src="assets/js/drag-drop.js"></script>

    <!-- 8. Font management methods -->
    <script src="assets/js/font-management.js"></script>

    <!-- 9. Background management methods -->
    <script src="assets/js/background-management.js"></script>

    <!-- 10. Export and canvas management -->
    <script src="assets/js/export-canvas.js"></script>

    <!-- 11. Utilities and initialization -->
    <script src="assets/js/utilities-init.js"></script>

    <!-- 12. Checkout and payment functions -->
    <script src="assets/js/checkout-payment.js"></script>

    <!-- Additional utility functions -->
    <script>
        // Back button functionality
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                // Fallback - redirect to parent directory
                window.location.href = '../';
            }
        }

        // Export billboard image function
        function exportBillboardImage() {
            const canvas = document.getElementById('cf7-canvas');
            if (!canvas) {
                alert('Canvas not found!');
                return;
            }

            // Show loading state
            const exportBtn = document.getElementById('cf7-export-image');
            const originalText = exportBtn.innerHTML;
            exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Exporting...';
            exportBtn.disabled = true;

            // Use html-to-image library to convert canvas to image
            htmlToImage.toPng(canvas, {
                quality: 1.0,
                pixelRatio: 2, // Higher resolution
                backgroundColor: '#ffffff',
                width: canvas.offsetWidth,
                height: canvas.offsetHeight,
                style: {
                    transform: 'scale(1)',
                    transformOrigin: 'top left'
                }
            })
            .then(function (dataUrl) {
                // Create download link
                const link = document.createElement('a');
                link.download = 'billboard-design-' + new Date().getTime() + '.png';
                link.href = dataUrl;

                // Trigger download
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Reset button
                exportBtn.innerHTML = originalText;
                exportBtn.disabled = false;

                // Show success message
                alert('Billboard image downloaded successfully!');
            })
            .catch(function (error) {
                console.error('Export failed:', error);
                alert('Failed to export image. Please try again.');

                // Reset button
                exportBtn.innerHTML = originalText;
                exportBtn.disabled = false;
            });
        }
    </script>

</body>
</html>