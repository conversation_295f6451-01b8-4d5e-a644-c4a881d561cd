// ========================================
// BACKGROUND CONTROL SETUP
// ========================================

// Extend CF7TextEditor class with background control methods
Object.assign(CF7TextEditor.prototype, {
    setupBackgroundControls() {
            // Get modal elements
            const modalTrigger = document.getElementById('cf7-bg-modal-trigger');
            const modal = document.getElementById('cf7-bg-modal');
            const modalClose = document.getElementById('cf7-modal-close');
            const modalCancel = document.getElementById('cf7-modal-cancel');
            const modalApply = document.getElementById('cf7-modal-apply');
            const clearButton = document.getElementById('cf7-clear-bg');

            if (!modalTrigger || !modal || !clearButton) {
                console.error('Modal elements not found');
                return;
            }

            // Store modal references
            this.modal = {
                element: modal,
                trigger: modalTrigger,
                close: modalClose,
                cancel: modalCancel,
                apply: modalApply,
                selectedCategory: null,
                selectedTemplate: null,
                focusedElementBeforeModal: null
            };

            // Set up event listeners
            modalTrigger.addEventListener('click', () => this.openBackgroundModal());
            modalClose.addEventListener('click', () => this.closeBackgroundModal());
            modalCancel.addEventListener('click', () => this.closeBackgroundModal());
            modalApply.addEventListener('click', () => this.applySelectedBackground());
            clearButton.addEventListener('click', () => this.clearBackground());

            // Setup modal content
            this.setupModalContent();
            this.setupModalKeyboardNavigation();
        }
});